package com.ruoyi.service.plan.processNode;

import com.ruoyi.entity.map.ClosestNode;
import com.ruoyi.entity.map.NearNode;
import com.ruoyi.entity.map.vo.CalcRouteVo;
import com.ruoyi.entity.map.vo.NearbyDeviceInfoVo;
import com.ruoyi.entity.map.vo.NearbySubstationInfoVo;
import com.ruoyi.graph.Node;
import com.ruoyi.service.map.impl.MapServiceImpl;
import com.ruoyi.util.map.LineDistanceQuery;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.poi.ss.formula.functions.T;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.function.Function;

/**
 * 加工附近节点处理 TODO 以下代码临时测试 等待刘士礼实现更改优化
 */
@Component
public class ProcessNearNode {

    @Autowired
    MapServiceImpl mapService;

    /**
     * 加工设置附近设备距离最近的节点
     */
    public void processDevRoute(NearNode nearNode, List<NearbyDeviceInfoVo> nearDevs, String token) {

        Node node = nearNode.getNode();
        double[] sourceLngLat = nearNode.getSourceLngLat();
        List<ClosestNode> closestNodes = new ArrayList<>();

        // 提前过滤掉较远的设备，直接比较两个经纬度集合点距离 找出较近5条设备集合
        double[] nodeSourceLngLat = nearNode.getSourceLngLat();
        // 小顶堆 记录最小的五个设备集合
        


        for (NearbyDeviceInfoVo deviceInfoVo : nearDevs) {
            double[] lngLat = deviceInfoVo.getLngLat();
            double distance = LineDistanceQuery.calculateHaversineDistance(nodeSourceLngLat[0], nodeSourceLngLat[1], lngLat[0], lngLat[1]);

        }
        List<CalcRouteVo> calcRouteVos = getRoutes(nearNode, token, nearDevs, NearbyDeviceInfoVo::getLngLat);

        // 将 CalcRouteVo 转为 ClosestNode
        for (int i = 0; i < calcRouteVos.size(); i++) {
            CalcRouteVo routeVo = calcRouteVos.get(i);
            NearbyDeviceInfoVo devInfo = nearDevs.get(i);
            if (routeVo == null || routeVo.getTotalLength() == null || routeVo.getTotalLength()
                    == 0.0) {
                continue;
            }

            ClosestNode closestNode = new ClosestNode(node, devInfo, sourceLngLat, devInfo.getLngLat());

            //  closestNode.setFeederId(devInfo.getFeederId());
            //  closestNode.setFeederName(devInfo.getFeederName());
            closestNode.setLength(routeVo.getTotalLength());
            closestNode.setRouteNodeList(routeVo.getNodeList());

            closestNodes.add(closestNode);
        }
        handleNearNode(nearNode, closestNodes);
    }

    /**
     * 加工设置附近变电站距离最近的节点
     */
    public void processBdzRoute(NearNode nearNode, List<NearbySubstationInfoVo> nearBdzs, String token) {

        double[] sourceLngLat = nearNode.getSourceLngLat();
        Node node = nearNode.getNode();
        List<ClosestNode> closestNodes = new ArrayList<>();
        List<CalcRouteVo> calcRouteVos = getRoutes(nearNode, token, nearBdzs, NearbySubstationInfoVo::getLngLat);

        // 将 CalcRouteVo 转为 ClosestNode
        for (int i = 0; i < calcRouteVos.size(); i++) {
            CalcRouteVo routeVo = calcRouteVos.get(i);
            NearbySubstationInfoVo bdz = nearBdzs.get(i);
            if (routeVo == null || routeVo.getTotalLength() == null || routeVo.getTotalLength()
                    == 0.0) {
                continue;
            }

            ClosestNode closestNode = new ClosestNode(node, bdz, sourceLngLat, bdz.getLngLat());
            closestNode.setLength(routeVo.getTotalLength());
            closestNode.setRouteNodeList(routeVo.getNodeList());
            closestNode.setBdzId(bdz.getPsrId());
            closestNode.setBdzName(bdz.getName());

            closestNodes.add(closestNode);
        }
        handleNearNode(nearNode, closestNodes);
    }

    public <T> List<CalcRouteVo> getRoutes(NearNode nearNode, String token, List<T> list, Function<T, double[]> getLngLat) {
        List<double[]> starts = new ArrayList<>();
        List<double[]> ends = new ArrayList<>();
        double[] sourceLngLat = nearNode.getSourceLngLat();

        for (T t : list) {
            double[] lngLat = getLngLat.apply(t);
            starts.add(sourceLngLat);
            ends.add(lngLat);
        }

        return mapService.calculateRouteAsync(starts, ends, token);
    }

    public void handleNearNode(NearNode nearNode, List<ClosestNode> closestNodes) {
        if (CollectionUtils.isEmpty(closestNodes)) {
            return;
        }

        // 排序 距离越近越优先
        closestNodes.sort(Comparator.comparingDouble(ClosestNode::getLength));

        nearNode.setRouteClosestNodes(closestNodes);
        nearNode.setRouteClosestNode(closestNodes.get(0));
    }
}

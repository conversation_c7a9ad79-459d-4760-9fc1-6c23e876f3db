package com.ruoyi.service.plan.findLay.contactLay.utils;

import com.ruoyi.graph.BranchNode;
import com.ruoyi.graph.Node;
import com.ruoyi.graph.NodePath;
import com.ruoyi.graph.utils.NodeUtils;
import com.ruoyi.service.plan.model.HwgBayBo;
import com.ruoyi.service.plan.model.ProcessContactBo;
import com.ruoyi.service.plan.model.SegBreakNodeBo;
import com.ruoyi.service.plan.model.findLay.ContactBranch;
import com.ruoyi.util.ListUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * lay查找确定要插入节点的工具类
 */
public class LayPositionUtil {

    /**
     * 根据大分子节点 获取当前大分子可以出线的联络点
     * （大分子由多条路径构成，每个路径的末端作为联络点即可）
     *
     * @param contactBranch 需要新增的大分子
     * @param nodePath
     */
    public static List<ProcessContactBo> getPContactByContactLay(ContactBranch contactBranch, NodePath nodePath) {

        // 获取当前分支点所有的路径
        List<List<Node>> paths = BranchPathsUtils.getBranchPaths(contactBranch, nodePath);

        List<ProcessContactBo> processContactBos = paths.stream().map(path -> getPContactByPaths(path, nodePath)).filter(Objects::nonNull).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(processContactBos)) {
            return new ArrayList<>();
        }

        // 相同的环网柜或者开关站 只需一个即可 这里需要过滤掉
        processContactBos = ListUtils.distinctByCustom(processContactBos, LayPositionUtil::layContactEqual);

        // 相同的类型  路径可能会重复  看看需不需要过滤掉呢

        // 进行排序 然后取最优的那个
        List<List<Node>> finalPaths = paths;
        processContactBos.sort((d1, d2) -> {
            int typeTotal = d2.getScopeByType() - d1.getScopeByType();
            // 过滤路径
            if (typeTotal == 0) {
                Node node1 = null, node2 = null;
                // 查找路径长度  如果是杆塔或者环网柜间隔 那么可以在根据路径长度排序
                if (StringUtils.equals(d1.getType(), ProcessContactBo.POLE_RANGE)) {
                    node1 = d1.getPole();
                    node2 = d2.getPole();
                }
//                else if (StringUtils.equals(d1.getType(), ProcessContactBo.STATION_BAY)) {
//                    node1 = d1.getHwgBay().getBayNodes().get(0);
//                    node2 = d2.getHwgBay().getBayNodes().get(0);
//                }
                if (node1 != null && node2 != null) {
                    int nodeIndex1 = NodeUtils.findNodeIndex(NodeUtils.findPathByPaths(finalPaths, node1), node1);
                    int nodeIndex2 = NodeUtils.findNodeIndex(NodeUtils.findPathByPaths(finalPaths, node2), node2);
                    return nodeIndex2 - nodeIndex1;
                }
            }

            return typeTotal;
        });

        return processContactBos;
    }

    /**
     * 根据路径获取联络线插入的位置
     *
     * @param paths 路径
     */
    public static ProcessContactBo getPContactByPaths(List<Node> paths, NodePath nodePath) {

        // =========================== 环网柜剩余间隔 =======================
        List<HwgBayBo> hwgStations = NodeUtils.getContactStation(paths);
        List<Node> contactKgNodes = nodePath.getContactKgNodes();
        List<HwgBayBo> hwgBays = null;
        if (hwgStations != null) {
            // 提前有剩余间隔的环网柜
            hwgBays = hwgStations.stream().filter(n ->
                    !CollectionUtils.isEmpty(n.getBayNodes()) && n.getBayNodes().size() >= 1
            ).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(hwgBays)) {
                // TODO 需要排序 然后在取第一个 排序条件：线经大小（架空线240，电缆线400）、当前站房也不建议有其他的联络线、尽量末端（路径长度）
                // 站房内是否有其他联络开关的排序
                hwgBays.sort((d1, d2) -> {
                    boolean has1 = hasHwgOtherContact(d1, contactKgNodes);
                    boolean has2 = hasHwgOtherContact(d2, contactKgNodes);
                    int scope1 = has1 ? 1 : 2;
                    int scope2 = has2 ? 1 : 2;
                    return scope2 - scope1;
                });
                return ProcessContactBo.createHwgBay(hwgBays.get(0));
            }
        }

        // =========================== 杆塔 =======================

        // 获取最后一个杆塔
        Node pole = null;
        Node tmpPole = null;
        for (int i = paths.size() - 1; i > 0; i--) {
            Node n = paths.get(i);
            if (n.isPole()) {
                if (n.getEdges().size() <= 2) {
                    pole = paths.get(i);
                    break;
                } else {
                    if (tmpPole == null) {
                        tmpPole = n;
                    }
                }
            }
        }
        if (pole == null && tmpPole != null) {
            pole = tmpPole;
        }
        // 杆塔
        if (pole != null) {
            return ProcessContactBo.createPoles(pole);
        }

        // =========================== 新增新的环网柜 =======================
        SegBreakNodeBo segBreakNode = getSegBreakNode(paths);
        if (segBreakNode != null) {
            return ProcessContactBo.createSegBreakNode(segBreakNode);
        }

        return null;
    }


    /**
     * 放置联络比较
     */
    private static boolean layContactEqual(ProcessContactBo pContact1, ProcessContactBo pContact2) {
        if (!StringUtils.equals(pContact1.getType(), pContact2.getType())) {
            return false;
        }
        String type = pContact1.getType();

        if (StringUtils.equals(type, ProcessContactBo.POLE_RANGE)) {
            return pContact1.getPole().equals(pContact2.getPole().getId());
        } else if (StringUtils.equals(type, ProcessContactBo.STATION_BAY)) {
            // 剩余间隔 比较是否同一个环网柜即可
            HwgBayBo hwgBay1 = pContact1.getHwgBay();
            HwgBayBo hwgBay2 = pContact2.getHwgBay();
            return hwgBay1.getHwg().equals(hwgBay2.getHwg().getId());
        } else if (StringUtils.equals(type, ProcessContactBo.SEG_ADD_HWG)) {
            // 新增环网柜
            SegBreakNodeBo segBreakNode1 = pContact1.getSegBreakNode();
            SegBreakNodeBo segBreakNode2 = pContact2.getSegBreakNode();

            // 比较开始和结束节点必须相同
            return segBreakNode1.getStartNode().equals(segBreakNode2.getStartNode().getId())
                    && segBreakNode1.getEndNode().equals(segBreakNode2.getEndNode().getId());
        }
        return false;
    }

    /**
     * 获取导线段打断节点
     */
    private static SegBreakNodeBo getSegBreakNode(List<Node> paths) {
        // 我们从末尾段往里走
        if (paths.size() < 3) {
            return null;
        }
        // 从联络站房开始
        int startIndex = -1;
        for (int i = 0; i < paths.size() - 1; i++) {
            Node node = paths.get(i);
            if (node.isContactStationInNode() && i > 0 && i < paths.size() - 1) {
                startIndex = i;
            }
        }

        // 环网柜开始 没有 就从开开始知道没有开关或者杆塔后面
        if (startIndex == -1) {
            for (int i = 1; i < paths.size() - 2; i++) {
                Node node = paths.get(i);
                // 不等于杆塔或者开关就开始设置
                if (!node.isEdge() && !(node.isPole() || node.isKg("all"))) {
                    startIndex = i;
                    break;
                }
            }
        }

        if (startIndex > -1) {
            for (int i = startIndex; i < paths.size() - 1; i++) {
                Node node = paths.get(i);
                if (node.isSegFeeder()) {
                    Node end = paths.get(i + 1);
                    Node start = paths.get(i - 1);
                    return new SegBreakNodeBo(start, node, end);
                }
            }
        } else {
            // 从后面开始
            for (int i = paths.size() - 2; i > 0; i--) {
                Node node = paths.get(i);
                if (node.isSegFeeder()) {
                    return new SegBreakNodeBo(paths.get(i - 1), node, paths.get(i + 1));
                }
            }
        }

        return null;
    }


    /**
     * 判断当前环网柜暂房内 是否有其他联络点
     *
     * @param hwgBay         环网柜间隔
     * @param contactKgNodes 联络开关集合
     */
    private static boolean hasHwgOtherContact(HwgBayBo hwgBay, List<Node> contactKgNodes) {
        Node hwg = hwgBay.getHwg();

        List<Node> children = hwg.getChildren();
        if (CollectionUtils.isEmpty(children)) {
            return true;
        }
        // 当前站房内不能有其他联络开关
        return ListUtils.hasMatchingData(children, contactKgNodes, (n1, n2) -> n1.equals(n2.getId()));
    }

}

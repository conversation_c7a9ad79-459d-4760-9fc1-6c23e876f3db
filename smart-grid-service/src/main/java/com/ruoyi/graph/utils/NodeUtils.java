package com.ruoyi.graph.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.constant.DeviceConstants;
import com.ruoyi.constant.PlanConstants;
import com.ruoyi.entity.map.SingAnalysis;
import com.ruoyi.entity.plan.vo.PlanOperateData;
import com.ruoyi.entity.znap.ZnapTopology;
import com.ruoyi.graph.Node;
import com.ruoyi.graph.NodePath;
import com.ruoyi.graph.SegBetween;
import com.ruoyi.graph.vo.NodeVo;
import com.ruoyi.graph.vo.SegBetweenVo;
import com.ruoyi.service.plan.model.HwgBayBo;
import com.ruoyi.util.ListUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.locationtech.jts.geom.*;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.ruoyi.util.coordinates.CoordinateConverter.*;

public class NodeUtils {

    /**
     * 节点集合转返回到NodeVo
     */
    public static ArrayList<NodeVo> toNodeVos(List<Node> nodes) {
        if (nodes == null) {
            return null;
        }
        ArrayList<NodeVo> result = new ArrayList<>();
        for (Node node : nodes) {
            result.add(toNodeVo(node));
        }
        return result;
    }

    /**
     * 节点集合转返回到NodeVo
     */
    public static NodeVo toNodeVo(Node node) {
        if (node == null) {
            return null;
        }
        // 电网设备的ID

        NodeVo nodeVo = new NodeVo(getPsrNodeId(node), node.getPsrId(), node.getPsrType(), node.getPsrName(), node.isEdge(), node.getType(), node.getShapeKey());

        nodeVo.setPorts(node.getPorts());
        nodeVo.setSourcePort(node.getSourcePort());
        nodeVo.setTargetPort(node.getTargetPort());
        nodeVo.setCap(node.getCap());
        nodeVo.setPubPrivFlag(node.getPubPrivFlag());
        // 处理坐标
        Geometry geometry = node.getGeometry();
        if (geometry != null) {
            if (node.isEdge()) {
                List<Double> doubleList = convertGeometryToDoubleList(geometry);
                // 转换为N/2 x 2的二维数组（假设元素数量是偶数）
                double[][] coordinates = new double[doubleList.size() / 2][2];
                for (int i = 0; i < doubleList.size(); i += 2) {
                    coordinates[i / 2][0] = doubleList.get(i);    // 经度
                    coordinates[i / 2][1] = doubleList.get(i + 1);  // 纬度
                }
                nodeVo.setCoordinates(coordinates);
            } else {
                nodeVo.setCoordinates(convertGeometryToDoubleList(geometry));
            }
        }

        // 处理子级
        if (CollectionUtils.isNotEmpty(node.getChildren())) {
            nodeVo.setChildrenIds(
                    node.getChildren().stream().map(NodeUtils::getPsrNodeId).collect(Collectors.toList())
            );
        }

        // 处理父级
        if (node.getParent() != null) {
            nodeVo.setParentId(getPsrNodeId(node.getParent()));
        }

        // 处理设备的边
        if (CollectionUtils.isNotEmpty(node.getEdges())) {
            List<Node> edges = node.getEdges().stream().filter(Objects::nonNull).collect(Collectors.toList());
            nodeVo.setEdgeIds(
                    edges.stream().map(n -> getPsrNodeId(n)).collect(Collectors.toList())
            );
        }
        if (node.isEdge()) {
            nodeVo.setLineType(node.getLineType());
        }

        nodeVo.setProperties(node.getProperties());

        nodeVo.setSourceId(node.getSource() != null ? getPsrNodeId(node.getSource()) : null);
        nodeVo.setTargetId(node.getTarget() != null ? getPsrNodeId(node.getTarget()) : null);
        return nodeVo;
    }

    /**
     * nodeVos转nodes
     */
    public static List<Node> toNodes(List<NodeVo> nodeVos) {
        if (nodeVos == null) {
            return null;
        }
        ArrayList<Node> result = new ArrayList<>();
        HashMap<String, Node> nodeMap = new HashMap<>();

        for (NodeVo nodeVo : nodeVos) {
            // 电网设备的ID

            Node node = new Node(nodeVo.getId(), nodeVo.getPsrId(), nodeVo.getPsrType(), nodeVo.getName(), nodeVo.isEdge(), nodeVo.getType(), nodeVo.getLineType(), nodeVo.getShapeKey());

            // 处理坐标
            Object coords = nodeVo.getCoordinates();
            if (coords != null) {
                if (node.isEdge()) {
                    LineString lineString = null;
                    if (coords instanceof double[][]) {
                        // 情况1：原生二维数组 double[][]
                        lineString = toLineString((double[][]) coords);
                    } else if (coords instanceof JSONArray) {
                        // 情况2：FastJSON 的 JSONArray
                        jsonArrayToLineString((JSONArray) coords);
                    } else if (coords instanceof List && !((List<?>) coords).isEmpty() && ((List<?>) coords).get(0) instanceof List) {
                        //情况3：(List<List<Double>>)
                        lineString = toLineString((List<List<Double>>) coords);
                    } else {
                        node.setGeometry(null);
                    }
                    node.setGeometry(lineString);
                } else {
                    Point point = null;
                    if (coords instanceof List) {
                        List<?> coordsList = (List<?>) coords;
                        if (coordsList.isEmpty()) {
                            node.setGeometry(null);
                        }
                        Object firstElement = coordsList.get(0);
                        if (firstElement instanceof BigDecimal) {
                            List<Double> doubleList = new ArrayList<>();
                            // 处理 BigDecimal 列表
                            BigDecimal x = (BigDecimal) coordsList.get(0);
                            BigDecimal y = (BigDecimal) coordsList.get(1);
                            doubleList.add(x.doubleValue());
                            doubleList.add(y.doubleValue());
                            point = toPoint(doubleList);
                        } else if (firstElement instanceof Double) {
                            // 处理 Double 列表
                            List<Double> doubleList = (List<Double>) coordsList;
                            point = toPoint(doubleList);

                        } else {
                            node.setGeometry(null);
                        }
                    } else {
                        node.setGeometry(null);
                    }
                    node.setGeometry(point);
                }
            }
            nodeMap.put(node.getId(), node);
            result.add(node);
        }
        for (NodeVo nodeVo : nodeVos) {
            String parentId = nodeVo.getParentId();
            List<String> childrenIds = nodeVo.getChildrenIds();
            String sourceId = nodeVo.getSourceId();
            String targetId = nodeVo.getTargetId();
            List<String> edgeIds = nodeVo.getEdgeIds();

            Node node = nodeMap.get(nodeVo.getId());
            // 父
            if (StringUtils.isNotBlank(parentId)) {
                node.setParent(nodeMap.get(parentId));
            }

            // 子级
            if (CollectionUtils.isNotEmpty(childrenIds)) {
                List<Node> children = childrenIds.stream().map(id -> nodeMap.get(id)).collect(Collectors.toList());
                // node.setChildren(children.stream().filter(Objects::nonNull).collect(Collectors.toList()));
                node.setChildren(children);
            }

            // 原ID
            if (StringUtils.isNotBlank(sourceId)) {
                node.setSource(nodeMap.get(sourceId));
            }

            if (StringUtils.isNotBlank(targetId)) {
                node.setTarget(nodeMap.get(targetId));
            }

            node.setProperties(nodeVo.getProperties());

            // 链接边
            if (CollectionUtils.isNotEmpty(edgeIds)) {
                node.setEdges(edgeIds.stream().map(id -> nodeMap.get(id)).collect(Collectors.toList()));
            }
        }

        return result;
    }

    /**
     * 复制节点集合
     *
     * @param nodes
     * @return
     */
    public static List<Node> copyNodes(List<Node> nodes) {
        if (CollectionUtils.isEmpty(nodes)) {
            return nodes;
        }
        ArrayList<NodeVo> nodeVos = toNodeVos(nodes);
        return toNodes(nodeVos);
    }

    /**
     * znap电网节点的ID是 PD_开头的ID  我们这里获取解构后的ID（psrId）
     */
    public static String getPsrNodeId(Node node) {
        String id = ZnapUtils.parsePsrId(node.getId());
        return id != null ? id : node.getId();
    }

    /**
     * 去重
     */
    public static List<Node> duplicateNodes(List<Node> nodes) {
        List<Node> result = new ArrayList<>();
        Set<String> seenKeys = new HashSet<>();

        for (Node node : nodes) {
            if (seenKeys.add(node.getId())) {
                result.add(node);
            }
        }
        return result;
    }


    /**
     * segBetween转为segBetweenVo
     */
    public static ArrayList<SegBetweenVo> toSegBetweenVoList(List<SegBetween> segBetweenList) {
        if (segBetweenList == null) {
            return null;
        }
        ArrayList<SegBetweenVo> result = new ArrayList<>();

        for (SegBetween segBetween : segBetweenList) {
            SegBetweenVo segBetweenVo = new SegBetweenVo(segBetween.getId(), segBetween.getStartPsrId(), segBetween.getStartPsrType(), segBetween.getStartPsrName(), segBetween.getEndPsrId(), segBetween.getEndPsrType(), segBetween.getEndPsrName());

            segBetweenVo.setNodes(toNodeVos(segBetween.getNodes()));
            segBetweenVo.setMainOtherNodes(toNodeVos(segBetween.getMainOtherNodes()));
            segBetweenVo.setMainAllOtherNodes(toNodeVos(segBetween.getMainAllOtherNodes()));
            segBetweenVo.setPbNum(segBetween.getPbNum());

            result.add(segBetweenVo);
        }
        return result;
    }

    /**
     * 递归节点
     *
     * @param startNode     开始节点
     * @param useNodeMap    已使用节点
     * @param nextOnlyEdges 当前开始节点的往下遍历的下一个边集合
     * @param breakFunc     自定义打断递归方法
     */
    public static void loopNode(Node startNode, HashMap<String, Boolean> useNodeMap, List<Node> nextOnlyEdges, Function<Node, Boolean> breakFunc) {
        Node currentNode = startNode;

        // 递归获取查找
        while (currentNode != null) {
            if (breakFunc != null && breakFunc.apply(currentNode)) {
                break;
            }

            useNodeMap.put(currentNode.getId(), true);

            Node nextNode = null;
            List<Node> edges = null;

            // 母线当成设备处理
            boolean currentIsEdge = currentNode.isBus() ? false : currentNode.isEdge();

            if (currentIsEdge) { // 边
                Node source = currentNode.getLink(true);
                Node target = currentNode.getLink(false);

                // 下一个节点
                nextNode = source != null && useNodeMap.containsKey(source.getId()) ? target : source;
                if (nextNode == null) {
                    edges = currentNode.getEdges();
                }
            } else { // 设备节点
                edges = currentNode.getEdges();
            }
            if (edges != null) {
                // 过滤已经存在遍历过的路径
                edges = edges.stream().filter(n -> !useNodeMap.containsKey(n.getId())).collect(Collectors.toList());

                ArrayList<Node> nextEdges = null;
                // 过滤仅仅需要的下一个边
                if (CollectionUtils.isNotEmpty(nextOnlyEdges)) {
                    edges = edges.stream().filter(n -> nextOnlyEdges.contains(n)).collect(Collectors.toList());
                    List<Node> finalEdges = edges;
                    nextEdges = (ArrayList<Node>) nextOnlyEdges.stream().filter(n -> !finalEdges.contains(n)).collect(Collectors.toList());
                }

                // 调度继续循环
                for (Node edge : edges) {
                    loopNode(edge, useNodeMap, nextEdges, breakFunc);
                }
                nextNode = null;
            }
            currentNode = nextNode;
        }
    }

    /**
     * 回溯法递归生成所有有效的分割组合
     *
     * @param current 当前处理的位置（前缀和索引）
     * @param prefix  前缀和数组
     * @param path    当前路径（存储分割点）
     * @param result  所有有效组合的集合
     */
    public static void backtrack(int current, int end, int[] prefix, List<Integer> path, List<List<Integer>> result, int max) {
        // 添加当前分割点（路径记录的是前缀和索引，实际位置需转换）
        path.add(current);

        // 终止条件：到达末尾（最后一个分支之后）
        if (current == end) {
            // 转换为实际开关位置（排除首尾的0和n）
            List<Integer> validCombination = new ArrayList<>();
            for (int i = 1; i < path.size() - 1; i++) {
                validCombination.add(path.get(i));
            }
            result.add(validCombination);
            path.remove(path.size() - 1);
            return;
        }

        // 尝试所有可能的下一个分割点
        for (int next = current + 1; next <= end; next++) {
            int sum = prefix[next] - prefix[current];
            if (sum >= max) break; // 超过阈值，后续更大区间也会超，直接终止
            backtrack(next, end, prefix, path, result, max);
        }

        // 回溯，移除当前分割点
        path.remove(path.size() - 1);
    }

    /**
     * 判断节点列表是否存在某个节点
     */
    public static boolean hasNode(List<ArrayList<Node>> nodeList, Node target) {
        if (nodeList == null) {
            return false;
        }
        for (ArrayList<Node> nodes : nodeList) {
            for (Node node : nodes) {
                if (node.equals(target)) {
                    return true;
                }
            }
        }
        return false;
    }

    /**
     * 判断节点列表是否存在某个节点
     */
    public static List<Node> findPathByPaths(List<List<Node>> paths, Node target) {
        if (paths == null) {
            return null;
        }
        for (List<Node> path : paths) {
            for (Node node : path) {
                if (node.equals(target)) {
                    return path;
                }
            }
        }
        return null;
    }

    public static Node findNode(List<Node> nodeList, Node target) {
        if (nodeList == null) {
            return null;
        }
        for (Node node : nodeList) {
            if (node.equals(target)) {
                return node;
            }
        }
        return null;
    }

    /**
     * 查找节点出现的下标
     */
    public static int findNodeIndex(List<Node> nodeList, Node target) {
        if (nodeList == null) {
            return -1;
        }

        for (int i = 0; i < nodeList.size(); i++) {
            if (nodeList.get(i).equals(target)) {
                return i;
            }
        }
        return -1;
    }

    /**
     * 截取数组
     *
     * @return
     */
    public static List<Node> subLiceNode(List<Node> nodeList, Node startNode, Node endNode) {

        if (nodeList == null) {
            return null;
        }

        if (startNode == null && endNode == null) {
            return null;
        }
        int startIndex = startNode != null ? findNodeIndex(nodeList, startNode) : 0;
        int endIndex = endNode != null ? findNodeIndex(nodeList, endNode) + 1 : nodeList.size();

        return nodeList.subList(startIndex, endIndex);
    }

    public static Node findNode(List<Node> nodeList, Function<Node, Boolean> equalFunc) {
        if (nodeList == null) {
            return null;
        }
        for (Node node : nodeList) {
            if (equalFunc.apply(node)) {
                return node;
            }
        }
        return null;
    }


    /**
     * 获取中间杆塔  如果是偶数 比如 4  那么会返回第2个
     */
    public static Node getMiddlePole(List<Node> nodes) {
        // 筛选出所有 type='aa' 的节点
        List<Node> filteredNodes = nodes.stream()
                .filter(Node::isPole)
                .collect(Collectors.toList());
        if (filteredNodes.size() == 0) {
            return null;
        }
        int mid = filteredNodes.size() / 2;

        if (filteredNodes.size() % 2 == 1) { // 基数
            return filteredNodes.get(mid);
        } else {
            return filteredNodes.get(mid);
        }
    }

    /**
     * 将节点集合转为方案的操作数据的json字符串
     */
    public static PlanOperateData toPlanOperateData(List<Node> nodes) {
        List<NodeVo> nodeVos = toNodeVos(nodes);
        List<NodeVo> edges = new ArrayList<>();
        List<NodeVo> devices = new ArrayList<>();
        for (NodeVo node : nodeVos) {
            if (node.isEdge()) {
                edges.add(node);
            } else {
                devices.add(node);
            }
        }

        return new PlanOperateData(edges, devices);
    }

    /**
     * 将方案的json的PlanOperate换原成List<node>
     */
    public static List<Node> planOperateJsonToNodes(String planOperateJson) throws JsonProcessingException {
        //将json还原成PlanOperateData
        ObjectMapper mapper = new ObjectMapper();
        PlanOperateData data = mapper.readValue(planOperateJson, PlanOperateData.class);
        //如果转换失败则返回空
        if (data == null) {
            return null;
        }
        //将PlanOperateData转成List<NodeVo>
        List<NodeVo> nodeVoList = data.getDevices();
        nodeVoList.addAll(data.getEdges());
        //如果解析的voList是空，则返回空
        if (CollectionUtils.isEmpty(nodeVoList)) {
            return null;
        }
        //将nodeVo转成node并返回
        List<Node> nodeList = toNodes(nodeVoList).stream()
                .filter(Objects::nonNull) // 过滤可能为 null 的 Node 对象
                .peek(node -> {
                    if (node.getId() != null) { // 避免 id 为 null 时 setPsrId 出问题
                        node.setPsrId(node.getId());
                    }//加else可以在id没值的情况下赋予默认值
                })
                .collect(Collectors.toList());
        return nodeList;

    }

    public static String toPlanOperateDataStr(List<Node> nodes) {
        return JSON.toJSONString(toPlanOperateData(nodes));
    }

    /**
     * 是否未使用的节点
     * 判断是否有“预留” || “备用” 名称即可
     */
    public static boolean isNotUsed(Node node) {
        String psrName = node.getPsrName();
        if (StringUtils.isBlank(psrName)) {
            return false;
        }
        return psrName.contains("预留") || psrName.contains("备用");
    }

    /**
     * 获取剩余间隔的开关
     *
     * @param station 站房节点
     */
    public static List<Node> getSurplusBayEndNode(Node station) {
        List<Node> children = station.getChildren();
        if (CollectionUtils.isEmpty(children)) {
            return null;
        }
        // 获取所有包含备用的开关
        List<Node> backupKgs = children.stream().filter(n -> n.isKg("all") && NodeUtils.isNotUsed(n)).collect(Collectors.toList());

        // 遍历备用开关 获取末端节点的开关（两个开关在同一个间隔的情况）
        ArrayList<Node> result = new ArrayList<>();

        for (Node backupKg : backupKgs) {
            Node bayEndNode = findBayEndNode(backupKg, new HashMap<>());
            result.add(bayEndNode == null ? backupKg : bayEndNode);
        }
        return result;
    }

    /**
     * 查找站房内 间隔末端设备
     * TODO（我们这里暂时设置开关，后续可能是终端了）
     */
    public static Node findBayEndNode(Node node, HashMap<String, Boolean> useNodeMap) {
        Node currentNode = node;

        Node result = null;

        // 递归获取查找
        while (currentNode != null) {
            useNodeMap.put(currentNode.getId(), true);

            // 新增当前的线路
            boolean isEdge = currentNode.isEdge();

            if (currentNode.isBus()) {
                return null;
            }
            if (currentNode.isKg("all")) {
                result = currentNode;
            }
            Node nextNode = null;

            List<Node> edges = null;
            if (isEdge) { // 边
                Node source = currentNode.getLink(true);
                Node target = currentNode.getLink(false);

                // 下一个节点
                nextNode = useNodeMap.containsKey(source.getId()) ? target : source;
                if (nextNode == null) {
                    edges = currentNode.getEdges();
                }
            } else { // 设备节点
                // 当前设备有那些边 我们继续往下递归
                edges = currentNode.getEdges();
            }
            if (edges != null) {
                // 过滤已经存在遍历过的路径
                edges = edges.stream().filter(n -> !useNodeMap.containsKey(n.getId())).collect(Collectors.toList());

                // 调度继续循环
                for (Node edge : edges) {
                    Node tmp = findBayEndNode(edge, useNodeMap);
                    if (tmp != null) {
                        return tmp;
                    }
                }
                nextNode = null;
            }
            currentNode = nextNode;
        }
        return result;
    }

    // 获取这条可以放在有剩余间隔的站房
    public static List<HwgBayBo> getContactStation(List<Node> paths) {

        if (CollectionUtils.isEmpty(paths)) {
            return null;
        }

        List<Node> hwgs = new ArrayList<>();

        for (Node node : paths) {
            Node parent = node.getParent();
            if (node.isContactStationInNode()) {
                hwgs.add(parent);
            }
        }
        // 去重
        ListUtils.distinctInPlace(hwgs, Node::getPsrId);

        // 过滤第一个为节点的环网柜(有些起始点是从环网柜开始的 那么当前环网柜不允许操作，需要过滤掉)
        Node first = paths.get(0);
        Node firstParent = paths.get(0).getParent();
        if (first.isContactStationInNode()) {
            hwgs = hwgs.stream().filter(n -> !n.equals(firstParent)).collect(Collectors.toList());
        }

        // 顺序颠倒  路径最长的在最前面
        Collections.reverse(hwgs);

        return hwgs.stream().map(hwg -> {
            List<Node> bayNodes = NodeUtils.getSurplusBayEndNode(hwg);
            return new HwgBayBo(hwg, bayNodes);
        }).collect(Collectors.toList());
    }

    /**
     * 深度比较路径
     */
    public static boolean deepEqualPaths(List<Node> nodes1, List<Node> nodes2) {

        // 处理任意一个为null的情况
        if (nodes1 == null || nodes2 == null) return false;

        // 比较列表长度
        if (nodes1.size() != nodes2.size()) return false;

        //  逐个元素比较
        for (int i = 0; i < nodes1.size(); i++) {
            if (!nodes1.get(i).equals(nodes2.get(i))) {
                return false;
            }
        }
        return true;
    }

    /**
     * 判断list1是否是list2的连续子序列（list2必须更长）
     */
    private static boolean isSubSequence(List<Node> list1, List<Node> list2) {
        int len1 = list1.size();
        int len2 = list2.size();

        // 安全校验：list1不能更长，且不能都为空
        if (len1 >= len2 || len1 == 0) {
            return false;
        }

        // 检查是否为连续子序列
        for (int i = 0; i <= len2 - len1; i++) {
            boolean match = true;
            for (int j = 0; j < len1; j++) {
                if (!list2.get(i + j).equals(list1.get(j))) {
                    match = false;
                    break;
                }
            }
            if (match) {
                return true;
            }
        }

        return false;
    }

    /**
     * 过滤二维路径中存在相同或包含关系的路径列表
     *
     * @param originPaths 原始二维路径
     * @return 过滤后的二维路径
     */
    public static List<List<Node>> filterSubPaths(List<List<Node>> originPaths) {
        List<List<Node>> result = new ArrayList<>();

        for (List<Node> current : originPaths) {
            // 标记是否被包含
            boolean isContained = false;

            // 只与更长的子列表比较是否被包含
            for (List<Node> other : originPaths) {
                // 跳过自身和长度小于等于当前子列表的列表
                if (current == other || other.size() <= current.size()) {
                    continue;
                }

                // 检查是否被更长的列表包含
                if (isSubSequence(current, other)) {
                    isContained = true;
                    break;
                }
            }

            // 被包含的子列表直接过滤
            if (isContained) {
                continue;
            }

            // 检查是否已存在于结果中（去重）
            boolean isDuplicate = false;
            for (List<Node> existing : result) {
                if (deepEqualPaths(current, existing)) {
                    isDuplicate = true;
                    break;
                }
            }

            // 不重复则添加到结果
            if (!isDuplicate) {
                result.add(current);
            }
        }

        return result;
    }

    /**
     * 路径之间比较是否有单方面完全重合
     */
    public static boolean equalPathSingleOverlap(List<Node> nodes1, List<Node> nodes2) {

        // 处理任意一个为null的情况
        if (nodes1 == null || nodes2 == null) return false;

        //  逐个元素比较
        for (int i = 0; i < nodes1.size(); i++) {
            if (!nodes1.get(i).equals(nodes2.get(i))) {
                return false;
            }
            // 如果某个路径已经到头了 前面也全部正确 那么就直接true
            if (i == (nodes1.size() - 1) || i == (nodes2.size() - 1)) {
                return true;
            }
        }
        return true;
    }

    /**
     * 当前节点路径 是否存在某个节点
     */
    public static int indexPathNode(ArrayList<Node> nodePaths, List<Node> someNodes) {
        for (int i = 0; i < nodePaths.size(); i++) {
            Node node = nodePaths.get(i);
            // 当前节点
            if (someNodes.stream().anyMatch(n -> n.equals(node.getId()))) {
                return i;
            }
        }
        return -1;
    }

    /**
     * 获取重叠的节点集合
     *
     * @param nodes1 节点集合1
     * @param nodes2 另一个节点集合
     */
    public static ArrayList<Node> getOverlapNodePaths(ArrayList<Node> nodes1, ArrayList<Node> nodes2) {
        ArrayList<Node> result = new ArrayList<>();
        int minSize = Math.min(nodes1.size(), nodes2.size());

        for (int i = 0; i < minSize; i++) {
            if (nodes1.get(i).equals(nodes2.get(i).getId())) {
                result.add(nodes1.get(i));
            } else {
                break;
            }
        }
        return result;
    }

    /**
     * 获取两个节点之间的边
     *
     * @param node1 节点1
     * @param node2 节点2
     * @return
     */
    public static Node getBetweenEdge(Node node1, Node node2) {
        List<Node> edges1 = node1.getEdges();
        List<Node> edges2 = node2.getEdges();
        if (CollectionUtils.isEmpty(edges1) || CollectionUtils.isEmpty(edges2)) {
            return null;
        }
        for (Node edge : edges1) {
            Node node = findNode(edges2, edge);
            if (node != null) {
                return node;
            }
        }
        return null;
    }

    /**
     * 判断两个节点集合 其中一个数组的节点在另一个数值里面也有
     */
    public static boolean hasCommonNode(List<Node> array1, List<Node> array2) {
        if (array1 == null || array2 == null) {
            return false;
        }

        Set<Node> set = new HashSet<>();
        for (Node node : array1) {
            set.add(node);
        }

        for (Node node : array2) {
            if (set.contains(node)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 比较两个Node列表是否相同（仅通过Node的equals(String otherId)方法比较id，不考虑顺序）
     *
     * @param list1 第一个Node列表
     * @param list2 第二个Node列表
     * @return 如果两个列表的id集合完全相同（不考虑顺序），返回true；否则返回false
     */
    public static boolean compareNodeLists(List<Node> list1, List<Node> list2) {
        // 处理引用相等或null的情况
        if (list1 == list2) return true;

        // 其中一个为 null
        if (list1 == null || list2 == null) {
            return false;
        }

        // 列表长度不同时直接返回false
        if (list1.size() != list2.size()) return false;

        // 使用HashMap统计元素出现次数(这么避免大数据  返回递归)
        Map<String, Integer> countMap = new HashMap<>(list1.size());
        // 统计list1中的元素
        for (Node node : list1) {
            countMap.merge(node.getId(), 1, Integer::sum);
        }

        // 检查list2中的元素
        for (Node node : list2) {
            Integer count = countMap.get(node.getId());
            if (count == null) {
                return false;
            }
            if (count == 1) {
                countMap.remove(node.getId());
            } else {
                countMap.put(node.getId(), count - 1);
            }
        }
        return countMap.isEmpty();
    }

    /**
     * 移除节点集合（会处理断开连接）
     *
     * @param removeNodes 需要移除的节点集合
     * @param nodeList    集合列表
     * @param nodeMap     节点psrId映射map
     */
    public static void removeNodes(List<Node> removeNodes, List<Node> nodeList, Map<String, Node> nodeMap) {
        // 需要删除的节点（包括子级）
        List<Node> delNodes = new ArrayList<>();
        for (Node node : removeNodes) {
            delNodes.add(node);
            delNodes.addAll(node.getAllChildList());
        }
        // 过滤重复的
        delNodes = ListUtils.distinctByKey(delNodes, Node::getId);

        // 删除和处理断开连接
        for (Node delNode : delNodes) {
            // 删除
            if (nodeList != null) {
                nodeList.remove(delNode);
            }

            if (nodeMap != null) {
                nodeMap.remove(delNode.getPsrId());
            }

            // 处理断开连接关系
            if (delNode.isEdge()) {
                // 需要删除的source 不在删除列表里 那么我们清楚链接关系
                if (delNode.getSource() != null && !delNodes.contains(delNode.getSource())) {
                    delNode.removeLink(true);
                }

                // 需要删除的target 不在删除列表里 那么我们清楚链接关系
                if (delNode.getTarget() != null && !delNodes.contains(delNode.getTarget())) {
                    delNode.removeLink(false);
                }
            } else {
                List<Node> edges = delNode.getEdges();
                if (CollectionUtils.isNotEmpty(edges)) {
                    for (Node edge : edges) {
                        // 处理链接边
                        if (!delNodes.contains(edge)) {
                            // 当前的边的source和当前需要删除的node相等 那么就需要断开
                            if (edge.getSource().equals(delNode)) {
                                delNode.removeEdge(edge, true);
                            }

                            // 当前的边的target和当前需要删除的node相等 那么就需要断开
                            if (edge.getTarget().equals(delNode)) {
                                delNode.removeEdge(edge, false);
                            }
                        }
                    }

                }
            }

        }
    }

    /**
     * 判断能具转供功能的联络开关节点
     */
    public static boolean canContactKgNode(Node node) {
        if (node == null) {
            return false;
        }
        boolean isOutKg = DeviceConstants.KG_OUT_TYPES.contains(node.getPsrType());
        // 表示站内开关
        Node station = node.getParent();
        if (isOutKg || station == null) {
            return true;
        }
        return node.isContactStationInNode();
    }

    /**
     * 根据下一条边 过滤当前边集合
     *
     * @param edges       需要过滤掉边集合
     * @param currentNode 当前节点
     * @param nextEdge    下一条边
     * @return
     */
    public static List<Node> filterNextEdges(List<Node> edges, Node currentNode, Node nextEdge) {
        if (nextEdge != null) {
            // 可能表示下一个节点不是边  我们就要尝试提取这两个节点之间的边
            if (!nextEdge.isEdge()) {
                Node betweenEdge = NodeUtils.getBetweenEdge(currentNode, nextEdge);
                if (betweenEdge != null) {
                    nextEdge = betweenEdge;
                }
            }
            Node tmpNode = nextEdge;
            // 在当前的边里面
            if (edges.stream().anyMatch(n -> n.equals(tmpNode.getId()))) {
                edges = Collections.singletonList(nextEdge);
            }
        }
        return edges;
    }

    public static void main(String[] args) {
        GeometryFactory geometryFactory = new GeometryFactory();
        double[][] coords = {{118, 31},
                {119, 32}};
        Coordinate[] collectList = Arrays.stream(coords)
                .map(lngLat -> new Coordinate(lngLat[0], lngLat[1]))
                .toArray(Coordinate[]::new);
        LineString lineString = geometryFactory.createLineString(collectList);

        Point point = geometryFactory.createPoint(new Coordinate(118.1, 31.1));

        Node edge = new Node("aa", lineString);
        edge.setEdge(true);
        Node device = new Node("bb", point);

        List<Node> list = Arrays.asList(edge, device);
        List<Node> nodes = NodeUtils.copyNodes(list);
        System.out.println(nodes);
    }
}

